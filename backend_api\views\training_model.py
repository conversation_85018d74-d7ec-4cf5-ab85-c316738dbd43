"""
训练模型管理相关的视图
"""

import logging
import json
from pathlib import Path
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
import time
import os

from backend_api.models.training_model import TrainingModel, ModelConversionLog, ModelInferenceLog
from backend_api.models.training import TrainingTask
from backend_api.serializers.training_model import (
    TrainingModelSerializer,
    TrainingModelCreateSerializer,
    TrainingModelUpdateSerializer,
    ModelConversionLogSerializer,
    ModelInferenceLogSerializer,
    ModelConversionRequestSerializer,
    ModelInferenceRequestSerializer,
    ModelListFilterSerializer,
    TrainingModelDetailSerializer
)
from utils.yolov8_docker_trainer import YOLOv8DockerTrainer
from utils.model_converter import ModelConverter

logger = logging.getLogger(__name__)


class TrainingModelListView(APIView):
    """训练模型列表视图"""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取训练模型列表"""
        try:
            # 验证查询参数
            filter_serializer = ModelListFilterSerializer(data=request.query_params)
            if not filter_serializer.is_valid():
                return Response({
                    'success': False,
                    'message': '查询参数无效',
                    'errors': filter_serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            filters = filter_serializer.validated_data
            
            # 构建查询
            queryset = TrainingModel.objects.all()
            
            # 应用过滤条件
            if 'task_id' in filters:
                queryset = queryset.filter(task_id=filters['task_id'])
            
            if 'export_status' in filters:
                queryset = queryset.filter(conversion_status=filters['export_status'])
                
            # 按是否已转换过滤
            if 'is_converted' in filters:
                is_converted = filters['is_converted']
                queryset = queryset.filter(is_converted=is_converted)
            
            if 'architecture' in filters:
                queryset = queryset.filter(architecture__icontains=filters['architecture'])
            
            if 'min_accuracy' in filters:
                queryset = queryset.filter(accuracy__gte=filters['min_accuracy'])
            
            if 'max_model_size' in filters:
                queryset = queryset.filter(model_size_mb__lte=filters['max_model_size'])
            
            if 'search' in filters:
                search_term = filters['search']
                queryset = queryset.filter(
                    Q(model_name__icontains=search_term) |
                    Q(notes__icontains=search_term) |
                    Q(architecture__icontains=search_term)
                )
            
            # 分页
            page = filters.get('page', 1)
            page_size = filters.get('page_size', 20)
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = TrainingModelSerializer(page_obj.object_list, many=True)
            
            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取训练模型列表失败: {e}")
            return Response({
                'success': False,
                'message': f'获取模型列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelDetailView(APIView):
    """训练模型详情视图"""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request, model_id):
        """获取模型详情"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            serializer = TrainingModelSerializer(model)
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取模型详情失败: {e}")
            return Response({
                'success': False,
                'message': f'获取模型详情失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, model_id):
        """更新模型信息"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            serializer = TrainingModelUpdateSerializer(model, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                
                # 返回更新后的数据
                response_serializer = TrainingModelSerializer(model)
                return Response({
                    'success': True,
                    'message': '模型信息更新成功',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': '更新参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"更新模型信息失败: {e}")
            return Response({
                'success': False,
                'message': f'更新模型信息失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, model_id):
        """删除模型"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            model_name = model.model_name
            model.delete()
            
            return Response({
                'success': True,
                'message': f'模型 {model_name} 删除成功'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"删除模型失败: {e}")
            return Response({
                'success': False,
                'message': f'删除模型失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelCreateView(APIView):
    """创建训练模型视图"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """创建新的训练模型记录"""
        try:
            serializer = TrainingModelCreateSerializer(data=request.data)
            if serializer.is_valid():
                # 生成模型名称：任务ID + 模型文件名
                task = serializer.validated_data.get('task')
                model_path = serializer.validated_data.get('model_path')

                if task and model_path:
                    model_filename = Path(model_path).stem
                    model_name = f"task_{task.id}_{model_filename}"
                else:
                    # 如果没有提供task_id或model_path，使用默认名称
                    model_name = f"model_{int(timezone.now().timestamp())}"

                # 创建模型记录
                model = serializer.save(
                    created_by=request.user,
                    model_name=model_name
                )
                
                # 返回创建的模型信息
                response_serializer = TrainingModelSerializer(model)
                return Response({
                    'success': True,
                    'message': '训练模型创建成功',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'message': '创建参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"创建训练模型失败: {e}")
            return Response({
                'success': False,
                'message': f'创建训练模型失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelConversionView(APIView):
    """模型转换视图"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """转换模型为OM格式"""
        try:
            # 记录请求数据，帮助调试
            logger.info(f"收到模型转换请求: {request.data}")
            
            serializer = ModelConversionRequestSerializer(data=request.data)
            if serializer.is_valid():
                model_id = serializer.validated_data['model_id']
                conversion_format = serializer.validated_data.get('conversion_format', 'om')
                chip_name = serializer.validated_data.get('chip_name', '910B3')
                
                logger.info(f"开始处理模型转换请求: model_id={model_id}, format={conversion_format}, chip={chip_name}")
                
                # 获取模型 - 不限制用户
                try:
                    model = TrainingModel.objects.get(id=model_id)
                    logger.info(f"找到模型: {model.model_name}, 路径: {model.model_path}")
                except TrainingModel.DoesNotExist:
                    logger.error(f"模型ID {model_id} 不存在")
                    return Response({
                        'success': False,
                        'message': f'模型ID {model_id} 不存在',
                        'status': 'failed'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # 创建转换日志
                conversion_log = ModelConversionLog.objects.create(
                    training_model=model,
                    status='started',
                    conversion_format=conversion_format,
                    created_by=request.user
                )
                
                # 更新模型状态
                model.conversion_status = 'processing'
                model.save()
                
                # 实际的模型转换逻辑
                logger.info(f"开始转换模型 {model.model_name} 为 {conversion_format} 格式，芯片型号: {chip_name}")

                try:
                    # 获取模型的服务器连接信息
                    server_ip = model.server_ip
                    server_port = model.server_port
                    server_password = model.server_password
                    
                    if not server_ip or not server_port or not server_password:
                        # 如果模型没有服务器信息，尝试从训练任务中获取
                        if hasattr(model, 'task') and model.task:
                            # 检查任务是否有服务器信息
                            task = model.task
                            
                            # 直接从任务中获取服务器信息
                            if hasattr(task, 'server_ip') and task.server_ip:
                                server_ip = task.server_ip
                                server_port = task.server_port
                                server_password = task.server_password
                            else:
                                # 从数据库中查询相同任务的其他模型
                                other_models = TrainingModel.objects.filter(task=task).exclude(id=model.id)
                                for other_model in other_models:
                                    if other_model.server_ip and other_model.server_port and other_model.server_password:
                                        server_ip = other_model.server_ip
                                        server_port = other_model.server_port
                                        server_password = other_model.server_password
                                        break
                                
                                if not server_ip or not server_port or not server_password:
                                    raise ValueError("无法获取服务器连接信息，请确保模型或训练任务中包含服务器信息")
                    
                    # 检查是否有服务器连接信息
                    if not server_ip or not server_port or not server_password:
                        raise ValueError("无法获取服务器连接信息")
                    
                    # 创建模型转换器
                    server_info = {
                        'ip': server_ip,
                        'port': server_port,
                        'password': server_password,
                        'username': 'root'  # 默认用户名
                    }
                    
                    logger.info(f"连接到服务器 {server_ip}:{server_port} 进行模型转换")
                    converter = ModelConverter(server_info)
                    
                    # 先进行上传文件
                    script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils", "model_info_extractor.py")
                    if not os.path.exists(script_path):
                        logger.warning(f"转换脚本不存在: {script_path}，尝试使用相对路径")
                        script_path = os.path.join("utils", "model_info_extractor.py")
                        if not os.path.exists(script_path):
                            logger.error(f"转换脚本不存在: {script_path}")
                            raise FileNotFoundError(f"转换脚本不存在: utils/model_info_extractor.py")
                    
                    logger.info(f"上传转换脚本: {script_path}")
                    converter.upload_script(script_path, "model_info_extractor.py")
                    
                    # 执行转换
                    conversion_result = converter.convert_model(
                        model_path=model.model_path,
                        chip_name=chip_name
                    )
                    
                    # 关闭连接
                    converter.disconnect()

                    # 更新转换日志
                    if conversion_result.get('success'):
                        conversion_log.status = 'completed'
                        conversion_log.output_path = conversion_result.get('output_path')
                        conversion_log.file_size_mb = float(conversion_result.get('file_size', '0').replace('M', '').replace('G', '000'))
                        conversion_log.conversion_log = conversion_result.get('export_log', '')
                        conversion_log.end_time = timezone.now()

                        # 更新模型状态
                        model.conversion_status = 'completed'
                        model.converted_model_path = conversion_result.get('output_path')
                        model.is_converted = True
                        model.save()

                        logger.info(f"✅ 模型转换成功: {conversion_result.get('output_path')}")
                    else:
                        conversion_log.status = 'failed'
                        conversion_log.error_message = conversion_result.get('error', '未知错误')
                        conversion_log.conversion_log = conversion_result.get('export_log', '')
                        conversion_log.end_time = timezone.now()

                        # 更新模型状态
                        model.conversion_status = 'failed'
                        model.save()

                        logger.error(f"❌ 模型转换失败: {conversion_result.get('error')}")

                    conversion_log.save()

                except Exception as e:
                    logger.error(f"转换过程中发生异常: {e}")
                    conversion_log.status = 'failed'
                    conversion_log.error_message = str(e)
                    conversion_log.end_time = timezone.now()
                    conversion_log.save()

                    model.conversion_status = 'failed'
                    model.save()
                
                return Response({
                    'success': True,
                    'message': '模型转换任务已启动',
                    'data': {
                        'conversion_log_id': conversion_log.id,
                        'model_id': model.id,
                        'conversion_format': conversion_format,
                        'status': conversion_log.status
                    },
                    'status': 'processing'
                }, status=status.HTTP_202_ACCEPTED)
            else:
                logger.warning(f"模型转换参数验证失败: {serializer.errors}")
                return Response({
                    'success': False,
                    'message': '转换参数无效',
                    'errors': serializer.errors,
                    'status': 'failed'
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"模型转换失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'模型转换失败: {str(e)}',
                'status': 'failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelInferenceView(APIView):
    """模型推理视图"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """使用模型进行推理"""
        try:
            # 记录请求数据，帮助调试
            logger.info(f"收到模型推理请求: {request.data}")
            
            serializer = ModelInferenceRequestSerializer(data=request.data)
            if serializer.is_valid():
                model_id = serializer.validated_data['model_id']
                input_source = serializer.validated_data.get('input_source', '')
                confidence_threshold = serializer.validated_data.get('confidence_threshold', 0.5)
                iou_threshold = serializer.validated_data.get('iou_threshold', 0.45)
                max_detections = serializer.validated_data.get('max_detections', 1000)
                save_result = serializer.validated_data.get('save_result', True)
                return_image = serializer.validated_data.get('return_image', True)  # 默认返回图片

                logger.info(f"开始处理模型推理请求: model_id={model_id}, input={input_source}")
                
                # 获取模型 - 不限制用户
                try:
                    model = TrainingModel.objects.get(id=model_id)
                    logger.info(f"找到模型: {model.model_name}, 路径: {model.model_path}")
                except TrainingModel.DoesNotExist:
                    logger.error(f"模型ID {model_id} 不存在")
                    return Response({
                        'success': False,
                        'message': f'模型ID {model_id} 不存在',
                        'status': 'failed'
                    }, status=status.HTTP_404_NOT_FOUND)

                # 实际的模型推理逻辑
                logger.info(f"开始使用模型 {model.model_name} 进行推理，输入源: {input_source}，置信度: {confidence_threshold}")

                try:
                    # 获取模型的服务器连接信息
                    server_ip = model.server_ip
                    server_port = model.server_port
                    server_password = model.server_password
                    
                    if not server_ip or not server_port or not server_password:
                        # 如果模型没有服务器信息，尝试从训练任务中获取
                        if hasattr(model, 'task') and model.task:
                            # 检查任务是否有服务器信息
                            task = model.task
                            if hasattr(task, 'server_ip') and task.server_ip:
                                server_ip = task.server_ip
                                server_port = task.server_port
                                server_password = task.server_password
                            else:
                                # 从数据库中查询相同任务的其他模型
                                other_models = TrainingModel.objects.filter(task=task).exclude(id=model.id)
                                for other_model in other_models:
                                    if other_model.server_ip and other_model.server_port and other_model.server_password:
                                        server_ip = other_model.server_ip
                                        server_port = other_model.server_port
                                        server_password = other_model.server_password
                                        break
                                
                                if not server_ip or not server_port or not server_password:
                                    raise ValueError("无法获取服务器连接信息，请确保模型或训练任务中包含服务器信息")
                    
                    # 检查是否有服务器连接信息
                    if not server_ip or not server_port or not server_password:
                        raise ValueError("无法获取服务器连接信息")
                    
                    # 创建模型转换器
                    server_info = {
                        'ip': server_ip,
                        'port': server_port,
                        'password': server_password,
                        'username': 'root'  # 默认用户名
                    }
                    
                    logger.info(f"连接到服务器 {server_ip}:{server_port} 进行模型推理")
                    converter = ModelConverter(server_info)
                    
                    # 构建输出路径
                    output_path = None
                    if save_result:
                        # 使用时间戳生成唯一的输出文件名
                        timestamp = int(time.time())
                        output_filename = f"inference_{model.id}_{timestamp}.jpg"
                        output_path = f"/workspace/{output_filename}"
                    
                    # 执行推理
                    inference_result = converter.run_inference(
                        model_path=model.model_path,
                        input_source=input_source,
                        confidence_threshold=confidence_threshold,
                        output_path=output_path
                    )
                    
                    # 关闭连接
                    converter.disconnect()

                    if inference_result.get('success'):
                        # 提取推理结果
                        detections = inference_result.get('detections', [])
                        detection_count = inference_result.get('detection_count', 0)
                        inference_time_ms = inference_result.get('inference_time_ms', 0.0)
                        result_path = inference_result.get('result_path', '')
                        
                        # 构建结果数据
                        result_data = {
                            'detections': detections,
                            'detection_count': detection_count,
                            'inference_time_ms': inference_time_ms,
                            'confidence_threshold': confidence_threshold,
                            'iou_threshold': iou_threshold,
                            'max_detections': max_detections,
                            'result_path': result_path
                        }
                        
                        # 如果需要返回图片，添加图片数据
                        if return_image:
                            if inference_result.get('has_image') and inference_result.get('image_data'):
                                result_data['image_data'] = inference_result.get('image_data')
                                result_data['image_format'] = inference_result.get('image_format', 'base64')
                                result_data['image_size_kb'] = inference_result.get('image_size_kb', 0)
                                result_data['has_image'] = True
                                logger.info(f"📷 返回图片数据，大小: {result_data['image_size_kb']:.2f} KB")
                            else:
                                result_data['image_data'] = None
                                result_data['image_format'] = None
                                result_data['image_size_kb'] = 0
                                result_data['has_image'] = False
                                result_data['image_message'] = inference_result.get('message', '图片数据不可用')
                                logger.warning("⚠️ 推理成功但无法获取图片数据")
                        
                        logger.info(f"✅ 推理成功，检测到 {detection_count} 个目标")
                    else:
                        # 推理失败
                        logger.error(f"❌ 推理失败: {inference_result.get('error')}")
                        result_data = {
                            'detections': [],
                            'detection_count': 0,
                            'inference_time_ms': 0.0,
                            'error': inference_result.get('error'),
                            'confidence_threshold': confidence_threshold,
                            'iou_threshold': iou_threshold,
                            'max_detections': max_detections
                        }

                except Exception as e:
                    logger.error(f"推理过程中发生异常: {e}")
                    # 构建错误结果
                    result_data = {
                        'detections': [],
                        'detection_count': 0,
                        'inference_time_ms': 0.0,
                        'error': str(e),
                        'confidence_threshold': confidence_threshold,
                        'iou_threshold': iou_threshold,
                        'max_detections': max_detections
                    }

                # 如果需要保存结果，创建推理日志
                if save_result:
                    inference_log = ModelInferenceLog.objects.create(
                        training_model=model,
                        input_source=input_source,
                        inference_time_ms=result_data.get('inference_time_ms', 0.0),
                        confidence_threshold=confidence_threshold,
                        detections_count=len(result_data.get('detections', [])),
                        result_data=result_data,
                        created_by=request.user
                    )

                    response_data = {
                        'inference_log_id': inference_log.id,
                        'model_id': model.id,
                        'model_name': model.model_name,
                        'result': result_data
                    }
                else:
                    response_data = {
                        'model_id': model.id,
                        'model_name': model.model_name,
                        'result': result_data
                    }

                return Response({
                    'success': True,
                    'message': '推理完成',
                    'data': response_data,
                    'status': 'completed'
                }, status=status.HTTP_200_OK)
            else:
                logger.warning(f"模型推理参数验证失败: {serializer.errors}")
                return Response({
                    'success': False,
                    'message': '推理参数无效',
                    'errors': serializer.errors,
                    'status': 'failed'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"模型推理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'模型推理失败: {str(e)}',
                'status': 'failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelConversionLogView(APIView):
    """模型转换日志视图"""

    permission_classes = [IsAuthenticated]
    
    def get(self, request, model_id=None):
        """获取模型转换日志"""
        try:
            if model_id:
                # 获取特定模型的转换日志
                model = get_object_or_404(TrainingModel, id=model_id)
                logs = ModelConversionLog.objects.filter(training_model=model)
            else:
                # 获取用户所有的转换日志
                user_models = TrainingModel.objects.filter(created_by=request.user)
                logs = ModelConversionLog.objects.filter(training_model__in=user_models)

            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            paginator = Paginator(logs.order_by('-start_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = ModelConversionLogSerializer(page_obj.object_list, many=True)
            
            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'num_pages': paginator.num_pages
                }
            })
        except Exception as e:
            logger.error(f"获取转换日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取转换日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelInferenceLogView(APIView):
    """模型推理日志视图"""

    permission_classes = [IsAuthenticated]

    def get(self, request, model_id=None):
        """获取模型推理日志"""
        try:
            if model_id:
                # 获取特定模型的推理日志
                model = get_object_or_404(
                    TrainingModel,
                    id=model_id,
                    created_by=request.user
                )
                logs = ModelInferenceLog.objects.filter(training_model=model)
            else:
                # 获取用户所有的推理日志
                user_models = TrainingModel.objects.filter(created_by=request.user)
                logs = ModelInferenceLog.objects.filter(training_model__in=user_models)

            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            paginator = Paginator(logs.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)

            # 序列化数据
            serializer = ModelInferenceLogSerializer(page_obj.object_list, many=True)

            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取推理日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取推理日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelViewSet(viewsets.ModelViewSet):
    """训练模型视图集"""
    queryset = TrainingModel.objects.all()
    serializer_class = TrainingModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'retrieve':
            return TrainingModelDetailSerializer
        return TrainingModelSerializer
    
    def get_queryset(self):
        """根据查询参数过滤模型列表"""
        queryset = TrainingModel.objects.all()
        
        # 按任务ID过滤
        task_id = self.request.query_params.get('task_id')
        if task_id:
            queryset = queryset.filter(task_id=task_id)
            
        # 按是否为最佳模型过滤
        is_best = self.request.query_params.get('is_best')
        if is_best:
            is_best_bool = is_best.lower() == 'true'
            queryset = queryset.filter(is_best=is_best_bool)
            
        # 按训练轮次过滤
        epoch = self.request.query_params.get('epoch')
        if epoch:
            queryset = queryset.filter(epoch=epoch)
            
        # 按是否已转换过滤
        is_converted = self.request.query_params.get('is_converted')
        if is_converted:
            is_converted_bool = is_converted.lower() == 'true'
            queryset = queryset.filter(is_converted=is_converted_bool)
            
        return queryset
    
    @action(detail=False, methods=['get'])
    def refresh_models(self, request):
        """
        从训练服务器刷新模型信息
        
        参数:
            task_id: 训练任务ID
        """
        task_id = request.query_params.get('task_id')
        if not task_id:
            return Response({"error": "必须提供任务ID"}, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            # 获取训练任务
            task = get_object_or_404(TrainingTask, id=task_id)
            
            # 查找与此任务关联的模型，以获取服务器信息
            server_ip = None
            server_port = None
            server_password = None
            
            # 首先检查任务本身是否有服务器信息
            if hasattr(task, 'server_ip') and task.server_ip:
                server_ip = task.server_ip
                server_port = task.server_port
                server_password = task.server_password
            else:
                # 查找关联的模型
                models = TrainingModel.objects.filter(task=task)
                for model in models:
                    if model.server_ip and model.server_port and model.server_password:
                        server_ip = model.server_ip
                        server_port = model.server_port
                        server_password = model.server_password
                        break
            
            if not server_ip or not server_port or not server_password:
                return Response({"error": "无法获取服务器连接信息"}, status=status.HTTP_404_NOT_FOUND)
            
            # 创建服务器连接信息
            server_info = {
                'ip': server_ip,
                'port': server_port,
                'password': server_password,
                'username': 'root'  # 默认用户名
            }
            
            # 创建ModelConverter实例
            converter = ModelConverter(server_info)
            
            # 获取模型信息
            # 注意：ModelConverter目前没有直接获取模型列表的方法
            # 这里需要添加一个新方法或使用现有方法的组合
            
            # 假设我们添加了一个get_models_info方法
            # models_info = converter.get_models_info(task.model_path)
            
            # 由于ModelConverter没有此方法，我们暂时返回已有的模型
            models = TrainingModel.objects.filter(task=task)
            serializer = self.get_serializer(models, many=True)
            
            # 关闭连接
            converter.disconnect()
            
            return Response({
                "message": "模型信息刷新成功",
                "models": serializer.data
            })
                
        except Exception as e:
            return Response({"error": f"刷新模型信息失败: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
