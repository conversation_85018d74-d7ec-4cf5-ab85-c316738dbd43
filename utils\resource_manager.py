import requests
from requests.auth import HTTPBasicAuth
import json
import re
import uuid  # 导入uuid模块用于生成唯一标识符

class ResourceManager:
    def __init__(self, base_url="https://192.168.2.99", username="admin", password="Sitonholy@2023"):
        self.base_url = base_url
        self.auth = HTTPBasicAuth(username, password)
        
    def query_products(self):
        """查询产品信息"""
        url = f"{self.base_url}/api/soc/api/product/query"
        response = requests.post(url, auth=self.auth, verify=False)
        return self._handle_response(response)
        
    def query_bucket_list(self, calculate_id, data_id=None, model_id=None):
        """查询存储挂载信息"""
        url = f"{self.base_url}/api/storage/bucketApi/queryBucketList"
        data = {
            "calculateId": calculate_id,
            "dataId": data_id,
            "modelId": model_id
        }
        response = requests.post(url, json=data, auth=self.auth, verify=False)
        return self._handle_response(response)
        
    def query_images(self, structure="amd64"):
        """查询镜像信息"""
        url = f"{self.base_url}/api/scr/imageApi/queryImage/{structure}"
        response = requests.post(url, auth=self.auth, verify=False) 
        return self._handle_response(response)
    
    def query_image_tags(self, page_size=0, page_num=0, type=1, power=0):
        """查询镜像标签列表"""
        url = f"{self.base_url}/api/satc/mirrorImage/queryImageTag"
        data = {
            "pageSize": page_size,
            "pageNum": page_num,
            "type": type,
            "power": power
        }
        response = requests.post(url, json=data, auth=self.auth, verify=False)
        return response.json()
    
    def create_task(self, calculate_device_id, specification_id, mirror_image, task_name=None, 
                   region_id="7f72ed7e4505636c11fa2d722fe9f067", specification_amount=1, 
                   gpu_mode=None, uplink_rate=0, downlink_rate=0, network_mode=0):
        """创建任务"""
        url = f"{self.base_url}/api/satc/task/create"
        
        # 如果没有提供任务名称，则生成一个基于UUID的名称
        if not task_name:
            task_name = f"Task-{uuid.uuid4().hex[:8]}"
            
        # 构建新的请求数据结构
        task_data = {
            "taskName": task_name,
            "regionId": region_id,
            "calculateDeviceId": calculate_device_id,
            "specificationId": specification_id,
            "specificationAmount": specification_amount,
            "gpuMode": gpu_mode,
            "uplinkRate": uplink_rate,
            "downlinkRate": downlink_rate,
            "mirrorImage": mirror_image,
            "networkMode": network_mode
        }
        
        # 最终的请求数据
        data = {
            "taskAddDTOS": [task_data]
        }
            
        response = requests.post(url, json=data, auth=self.auth, verify=False)
        return response.json()
    
    def delete_task(self, task_id):
        """删除任务"""
        url = f"{self.base_url}/api/satc/task/delete/{task_id}"
        response = requests.delete(url, auth=self.auth, verify=False)
        return response.json()
    
    def stop_task(self, task_id):
        """关闭任务/关机"""
        url = f"{self.base_url}/api/satc/task/stop/{task_id}"
        response = requests.put(url, auth=self.auth, verify=False)
        return response.json()
    
    def query_task(self, taskname):
        """查询任务信息"""
        url = f"{self.base_url}/api/satc/task/page"
        data = {"taskName": taskname,"pageReq": {"pageSize": 0,"pageNum": 0}}
        response = requests.post(url, json=data, auth=self.auth, verify=False)
        return self._handle_response(response)
    
    
    def _handle_response(self, response):
        """处理API响应"""
        try:
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == "0":
                return data.get("data")
            else:
                error_msg = data.get("msg", "Unknown error")
                raise Exception(f"API Error: {error_msg}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("Failed to parse response JSON")

def main():
    # 创建API客户端实例
    client = ResourceManager()
    
    try:
        # 1. 查询产品信息
        print("查询产品信息...")
        products = client.query_products()
        print(json.dumps(products, indent=2, ensure_ascii=False))
        # 接口返回结果如下：
        # {
        #     "code": "0",
        #     "msg": "",
        #     "data": [
        #         {
        #             "specificationId": "4a020ea522caa5e844330b910f0c7f0a",
        #             "hoseAlias": "node04-910B3",
        #             "gpuModel": "910B3",
        #             "cpuModel": "Kunpeng-920",
        #             "gpuMode": 1,
        #             "laveSpecificationAmount": 6,
        #             "cpuCoreAmount": 21,
        #             "gpuAmount": 1,
        #             "memory": 58,
        #             "hardDisk": 60,
        #             "cacheDisk": 60,
        #             "videoStorage": 64.0000,
        #             "toolkitVersion": "1",
        #             "platformArch": "aarch64",
        #             "uplinkRate": null,
        #             "downlinkRate": null,
        #             "unitPrice": null,
        #             "discount": null,
        #             "supportStorage": null,
        #             "calculateDeviceId": "66f9cf910f25b9ef7f7d0b656b62e959",
        #             "structure": "aarch64"
        #         },
        #     .......
        #     ]
        # }
        
        if products and len(products) > 0:
            # 获取第一个产品的信息
            product = products[0]
            print(product)
            specification_id = product.get("specificationId")
            calculate_id = product.get("calculateDeviceId")
            structure = product.get("structure")
            gupmode = product.get("gpuMode")
            
            # 2. 查询存储挂载信息
            print("\n查询存储挂载信息...")
            buckets = client.query_bucket_list(calculate_id)
            print(json.dumps(buckets, indent=2, ensure_ascii=False))
            # 返回结果如下：
            # {
            #     "code": "0",
            #     "msg": "",
            #     "data": [
            #         {
            #             "id": "",
            #             "fullPath": "/sitonholy/pub/",
            #             "storageType": 0,
            #             "mountPath": "/root/siton-pub",
            #             "perm": "ro",
            #             "type": 1
            #         },
            #         {
            #             "id": "1752219334",
            #             "fullPath": "/sitonholy/nfs/***********/nfs/users/b496463103254f46976c4ff88ea74bc9",
            #             "storageType": 0,
            #             "mountPath": "/root/siton-data-b496463103254f46976c4ff88ea74bc9",
            #             "perm": "rw",
            #             "type": 1
            #         }
            #     ]
            # }
            
            # 3. 查询镜像信息
            print("\n查询镜像信息...")
            images = client.query_images(structure)
            print(json.dumps(images, indent=2, ensure_ascii=False))
            # 结果返回如下：
            # [
            #     "***********:5000/yolov8/yolov8:test",
            #     "***********:5000/yolov8/yolo11:1753167940001",
            #     "***********:5000/inference/vllm-ascend:v0.9.2rc1-linuxarm64",
            #     "***********:5000/siton/910bxl:v1"
            # ]
            
            if images and len(images) > 0:
                # 4. 创建任务
                print("\n创建任务...")
                
                # 使用第一个存储桶作为示例  不需要该参数了
                # storage_list = buckets if buckets else []
                
                # 使用第一个yolov8镜像作为示例
                pattern = re.compile(r"yolov8:")
                mirror_image = [item for item in images if pattern.search(item)][0]

                
                # 创建任务（使用新的API格式）
                taskname = f"yolov8训练任务-{uuid.uuid4().hex[:8]}"
                client.create_task(
                    calculate_device_id=calculate_id,
                    specification_id=specification_id,
                    mirror_image=mirror_image,
                    gpu_mode=gupmode,
                    task_name=taskname
                )
                print("任务创建成功")
                # 返回结果如下：
                # {
                #     "code": "0",
                #     "msg": "",
                #     "data": ""
                # }

                # 5. 查询任务信息
                print("\n查询任务信息...")
                task_info = client.query_task(taskname)
                print(json.dumps(task_info, indent=2, ensure_ascii=False))
                # 返回结果如下：
                #{
                # "records": [
                #     {
                #     "id": "e5cccefc7b7fb4987e38601076e1017d",
                #     "taskName": "yolov8训练任务-9db2f09c",
                #     "status": 1,
                #     "calculateDeviceId": "66f9cf910f25b9ef7f7d0b656b62e959",
                #     "calculateDeviceIp": "node04-910B3",
                #     "specificationId": "4a020ea522caa5e844330b910f0c7f0a",
                #     "specificationAmount": 1,
                #     "runMode": 1,
                #     "costType": null,
                #     "createBy": "18888888880",
                #     "releaseTime": null,
                #     "cpuCoreAmount": 21,
                #     "gpuAmount": 1,
                #     "memory": 58,
                #     "hardDisk": 60,
                #     "cacheDisk": 60,
                #     "gpuId": "aa3819980c8a5b08ad75e8a8c767a1c2",
                #     "containerId": "e5cccefc7b7f",
                #     "gpuModel": "910B3",
                #     "uplinkRate": 0.0,
                #     "downlinkRate": 0.0,
                #     "orderExtentInfo": null,
                #     "orderPrice": null,
                #     "discount": null,
                #     "normalStartup": null,
                #     "agentDeviceId": "928c92edf857d45319f1ef768872d8c3",
                #     "agentIp": "***********",
                #     "sshPort": "20424",
                #     "sshPasswd": "7xxkDBITCw",
                #     "jupyterPort": "20748",
                #     "jupyterPasswd": "xCshZ55wKY",
                #     "vncPort": "20008",
                #     "vncPasswd": "GOsQDzmyXB",
                #     "vscodePort": "20144",
                #     "vscodePasswd": "EqCe6W0H4P",
                #     "tensorboardPort": "20128",
                #     "customPort": null,
                #     "sitonAiToolPort": "20462",
                #     "userSubId": "",
                #     "userSubName": null,
                #     "regionId": "fb1e97c05286924e87eede04c84a958b",
                #     "region": "山西",
                #     "userId": "eee6dd424c307c0c17bc0081ba438cb8",
                #     "phone": "18888888880",
                #     "email": "<EMAIL>",
                #     "remark": null,
                #     "orderExpireFlag": 0,
                #     "hostStatus": 0,
                #     "createTime": "2025-07-22 16:49:03",
                #     "updateTime": "2025-07-22 16:49:03",
                #     "containerImage": "yolov8/yolov8:test",
                #     "userAutn": false,
                #     "realNameAutn": "0",
                #     "structure": "aarch64",
                #     "gpuMode": 1,
                #     "gpuSerialNoS": [
                #         4
                #     ],
                #     "startTime": null,
                #     "shutdownTime": null,
                #     "runningTime": "0小时",
                #     "networkMode": 0,
                #     "vpcIp": null,
                #     "vpcPublicIp": null,
                #     "type": 1,
                #     "toolName": "SitonTools"
                #     }
                # ],
                # "total": 1,
                # "size": 10,
                # "current": 1,
                # "pages": 1
                # }

            
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()